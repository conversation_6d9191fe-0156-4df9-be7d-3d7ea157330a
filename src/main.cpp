#include <iostream>
#include <string>
#include <map>

#include "mqtt_client.hpp"

using namespace std;

/**
 * @brief               Handler on message from cloud
 * @param[in]   data    The message payload from cloud
 */
void on_cloud_message(const string& data)
{
    std::cout<<"received data is: "<<data<<std::endl;
}

int main(int argc,char **argv)
{
    cloud::mqtt_client g_client;                         //定义一个mqtt客户端
    std::cout << "[CLOUD] listen starting"<<std::endl;
    g_client.set_message_handler(on_cloud_message);      //开启mqtt clinet监听消息，消息处理函数为on_cloud_message

    while(1)
    {
        std::cout << "运行中..."<<std::endl;
        this_thread::sleep_for(10s);
        g_client.send("online...");  // 确保与mqtt broker server建立连接之后再publish!!!
    }
    return 0;
}
