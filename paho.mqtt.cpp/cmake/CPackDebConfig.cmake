if(<PERSON>ACK_GENERATOR MATCHES "DEB")
    set(CPACK_PACKAGE_NAME "libpaho-mqtt.cpp")
    set(CPACK_DE<PERSON>AN_PACKAGE_NAME ${CPACK_PACKAGE_NAME})
    set(CPACK_<PERSON>CKAGE_CONTACT "Eclipse")
    set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "Eclipse Paho MQTT C++ client")
    set(CPACK_DEBIAN_PACKAGE_MAINTAINER " <>")
    set(CPACK_DEBIAN_PACKAGE_SHLIBDEPS ON)
    set(CPACK_<PERSON><PERSON>AN_PACKAGE_VERSION ${PACKAGE_VERSION})
    set(CPACK_<PERSON><PERSON>AN_PACKAGE_SECTION "net")
endif()
