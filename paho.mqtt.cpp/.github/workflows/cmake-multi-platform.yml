# This starter workflow is for a CMake project running on multiple platforms. There is a different starter workflow if you just want a single platform.
# See: https://github.com/actions/starter-workflows/blob/main/ci/cmake-single-platform.yml
name: CMake on multiple platforms

on:
  push:
    branches: [ "master", "develop" ]
  pull_request:
    branches: [ "master", "develop" ]

jobs:
  build:
    runs-on: ${{ matrix.os }}

    strategy:
      # Set fail-fast to false to ensure that feedback is delivered for all matrix combinations. Consider changing this to true when your workflow is stable.
      fail-fast: false

      # Set up a matrix to run the following 3 configurations:
      # 1. <Windows, Release, latest MSVC compiler toolchain on the default 
      #    runner image, default generator>
      # 2. <Linux, Release, latest GCC compiler toolchain on the default
      #    runner image, default generator>
      # 3. <Linux, Release, latest Clang compiler toolchain on the default
      #     runner image, default generator>
      #
      # To add more build types (Release, Debug, RelWithDebInfo, etc.) customize the build_type list.
      matrix:
        os: [ubuntu-latest, windows-latest]
        build_type: [Release, Debug]
        c_compiler: [gcc, clang, cl]
        include:
          - os: windows-latest
            c_compiler: cl
            cpp_compiler: cl
          - os: ubuntu-latest
            c_compiler: gcc
            cpp_compiler: g++
          - os: ubuntu-latest
            c_compiler: clang
            cpp_compiler: clang++
        exclude:
          - os: windows-latest
            c_compiler: gcc
          - os: windows-latest
            c_compiler: clang
          - os: ubuntu-latest
            c_compiler: cl

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Install Mosquitto (ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-add-repository ppa:mosquitto-dev/mosquitto-ppa
        sudo apt-get update
        sudo apt-get -y install mosquitto

# TODO: Figure out how to install and run mosquitto on Windows for unit tests.

#    - name: Install Mosquitto from Source (windows)
#      if: matrix.os == 'windows-latest'
#      run: |
#        git clone https://github.com/eclipse/mosquitto.git
#        cd mosquitto
#        git checkout "v2.0.18"
#        mkdir build && cd build
#        cmake .. -DCMAKE_CXX_COMPILER=${{ matrix.cpp_compiler }}
#        cmake --build . --target install
#        "C:\Program Files\mosquitto\mosquitto install"

    - name: Install Catch2 v2 from Source (ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        git clone https://github.com/catchorg/Catch2.git
        cd Catch2
        git checkout "v2.13.8"
        mkdir build && cd build
        cmake .. -DBUILD_TESTING=OFF -DCMAKE_CXX_COMPILER=${{ matrix.cpp_compiler }}
        sudo cmake --build . --target install

    - name: Install Catch2 v2 from Source (windows)
      # For Windows, it's important to build Catch2 with the same 'build_type'
      # as the Paho C++ library, so that they link without errors.
      if: matrix.os == 'windows-latest'
      run: |
        git clone https://github.com/catchorg/Catch2.git
        cd Catch2
        git checkout "v2.13.8"
        mkdir build && cd build
        cmake .. -DBUILD_TESTING=OFF -DCMAKE_CXX_COMPILER=${{ matrix.cpp_compiler }}
        cmake --build . --config ${{ matrix.build_type }} --target install

    - name: Set reusable strings
      # Turn repeated input strings (such as the build output directory) into step outputs. 
      # These step outputs can be used throughout the workflow file.
      id: strings
      shell: bash
      run: |
        echo "build-output-dir=${{ github.workspace }}/build" >> "$GITHUB_OUTPUT"

    - name: Configure CMake
      # We configure to build the examples, unit tests, and Paho C library.
      #
      # Configure CMake in a 'build' subdirectory. `CMAKE_BUILD_TYPE` is only required
      # if you are using a single-configuration generator such as make.
      # See https://cmake.org/cmake/help/latest/variable/CMAKE_BUILD_TYPE.html?highlight=cmake_build_type
      run: >
        cmake -B ${{ steps.strings.outputs.build-output-dir }}
        -DCMAKE_CXX_COMPILER=${{ matrix.cpp_compiler }}
        -DCMAKE_C_COMPILER=${{ matrix.c_compiler }}
        -DCMAKE_BUILD_TYPE=${{ matrix.build_type }}
        -DPAHO_BUILD_EXAMPLES=ON
        -DPAHO_BUILD_TESTS=ON
        -DPAHO_WITH_MQTT_C=ON
        -S ${{ github.workspace }}

    - name: Build
      # Build your program with the given configuration. Note that --config is
      # needed for Windows because the default Windows generator is a multi-config
      # generator (Visual Studio generator).
      run: cmake --build ${{ steps.strings.outputs.build-output-dir }} --config ${{ matrix.build_type }}

    - name: Test
      if: matrix.os == 'ubuntu-latest'
      working-directory: ${{ steps.strings.outputs.build-output-dir }}
      # Execute tests defined by the CMake configuration. 
      # Note that --build-config is needed because the default Windows generator
      # is a multi-config generator (Visual Studio generator).
      # See https://cmake.org/cmake/help/latest/manual/ctest.1.html for more detail
      run: ctest --build-config ${{ matrix.build_type }}
